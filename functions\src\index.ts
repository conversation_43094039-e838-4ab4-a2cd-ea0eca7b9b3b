import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {google} from "googleapis";

// Correctly import the JSON file using the 'import' statement
import * as serviceAccount from "./service-account.json";

// Initialize Firebase Admin SDK
admin.initializeApp();
const firestore = admin.firestore();

// IMPORTANT: Replace with your app's actual package name
const packageName = "com.your.app.package_name";

/**
 * A callable Cloud Function to securely validate a Google Play purchase
 * and grant credits to the user.
 */
// The fix is here: Explicitly type 'data' and 'context'
export const validatePurchase = functions.https.onCall(
  async (data: any, context: functions.https.CallableContext) => {
    // 1. Check for authentication. The 'auth' property is now correctly typed.
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "You must be logged in to make a purchase.",
      );
    }

    // The 'data' object is now correctly understood to be the client payload.
    const {purchaseToken, productId} = data;
    const uid = context.auth.uid;

    // 2. Initialize the Google Play API client
    const authClient = new google.auth.GoogleAuth({
      credentials: {
        client_email: serviceAccount.client_email,
        private_key: serviceAccount.private_key,
      },
      scopes: ["https://www.googleapis.com/auth/androidpublisher"],
    });

    const androidPublisher = google.androidpublisher({
      version: "v3",
      auth: authClient,
    });

    try {
      // 3. Verify the purchase with Google Play Developer API
      const res = await androidpublisher.purchases.products.get({
        packageName: packageName,
        productId: productId,
        token: purchaseToken,
      });

      // 4. Perform crucial checks on the response
      if (res.data.purchaseState !== 0) { // 0 is PURCHASED
        throw new functions.https.HttpsError(
          "failed-precondition", "Purchase is not valid or is pending.");
      }

      if (res.data.consumptionState !== 0) { // 0 is NOT_CONSUMED
        throw new functions.https.HttpsError(
          "failed-precondition", "Purchase has already been consumed.");
      }

      // 5. Grant credits in a Firestore transaction
      const userDocRef = firestore.collection("users").doc(uid);
      let creditsToAdd = 0;
      if (productId === "calculation_credits_3") {
        creditsToAdd = 3;
      }
      // Add more else-if blocks here for other products

      if (creditsToAdd === 0) {
        throw new functions.https.HttpsError(
          "invalid-argument", "Unknown product ID.");
      }

      await firestore.runTransaction(async (transaction) => {
        const userDoc = await transaction.get(userDocRef);
        if (!userDoc.exists) {
          throw new functions.https.HttpsError(
            "not-found", "User document not found.");
        }
        const currentCredits = userDoc.data()?.calculationCredits || 0;
        const newCredits = currentCredits + creditsToAdd;
        transaction.update(userDocRef, {calculationCredits: newCredits});
      });

      // 6. Acknowledge the purchase with Google Play
      await androidpublisher.purchases.products.acknowledge({
        packageName: packageName,
        productId: productId,
        token: purchaseToken,
      });

      return {status: "success", newCredits: "updated"};
    } catch (error) {
      functions.logger.error("Error validating purchase:", error);
      throw new functions.https.HttpsError(
        "internal",
        "An error occurred while validating the purchase.",
        error,
      );
    }
  },
);
