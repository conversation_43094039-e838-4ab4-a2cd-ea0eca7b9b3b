/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace datastore_v1beta1 {
    export interface Options extends GlobalOptions {
        version: 'v1beta1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Cloud Datastore API
     *
     * Accesses the schemaless NoSQL database to provide fully managed, robust, scalable storage for your application.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const datastore = google.datastore('v1beta1');
     * ```
     */
    export class Datastore {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Metadata common to all Datastore Admin operations.
     */
    export interface Schema$GoogleDatastoreAdminV1beta1CommonMetadata {
        /**
         * The time the operation ended, either successfully or otherwise.
         */
        endTime?: string | null;
        /**
         * The client-assigned labels which were provided when the operation was created. May also include additional labels.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The type of the operation. Can be used as a filter in ListOperationsRequest.
         */
        operationType?: string | null;
        /**
         * The time that work began on the operation.
         */
        startTime?: string | null;
        /**
         * The current state of the Operation.
         */
        state?: string | null;
    }
    /**
     * Identifies a subset of entities in a project. This is specified as combinations of kinds and namespaces (either or both of which may be all, as described in the following examples). Example usage: Entire project: kinds=[], namespace_ids=[] Kinds Foo and Bar in all namespaces: kinds=['Foo', 'Bar'], namespace_ids=[] Kinds Foo and Bar only in the default namespace: kinds=['Foo', 'Bar'], namespace_ids=[''] Kinds Foo and Bar in both the default and Baz namespaces: kinds=['Foo', 'Bar'], namespace_ids=['', 'Baz'] The entire Baz namespace: kinds=[], namespace_ids=['Baz']
     */
    export interface Schema$GoogleDatastoreAdminV1beta1EntityFilter {
        /**
         * If empty, then this represents all kinds.
         */
        kinds?: string[] | null;
        /**
         * An empty list represents all namespaces. This is the preferred usage for projects that don't use namespaces. An empty string element represents the default namespace. This should be used if the project has data in non-default namespaces, but doesn't want to include them. Each namespace in this list must be unique.
         */
        namespaceIds?: string[] | null;
    }
    /**
     * Metadata for ExportEntities operations.
     */
    export interface Schema$GoogleDatastoreAdminV1beta1ExportEntitiesMetadata {
        /**
         * Metadata common to all Datastore Admin operations.
         */
        common?: Schema$GoogleDatastoreAdminV1beta1CommonMetadata;
        /**
         * Description of which entities are being exported.
         */
        entityFilter?: Schema$GoogleDatastoreAdminV1beta1EntityFilter;
        /**
         * Location for the export metadata and data files. This will be the same value as the google.datastore.admin.v1beta1.ExportEntitiesRequest.output_url_prefix field. The final output location is provided in google.datastore.admin.v1beta1.ExportEntitiesResponse.output_url.
         */
        outputUrlPrefix?: string | null;
        /**
         * An estimate of the number of bytes processed.
         */
        progressBytes?: Schema$GoogleDatastoreAdminV1beta1Progress;
        /**
         * An estimate of the number of entities processed.
         */
        progressEntities?: Schema$GoogleDatastoreAdminV1beta1Progress;
    }
    /**
     * The request for google.datastore.admin.v1beta1.DatastoreAdmin.ExportEntities.
     */
    export interface Schema$GoogleDatastoreAdminV1beta1ExportEntitiesRequest {
        /**
         * Description of what data from the project is included in the export.
         */
        entityFilter?: Schema$GoogleDatastoreAdminV1beta1EntityFilter;
        /**
         * Client-assigned labels.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Location for the export metadata and data files. The full resource URL of the external storage location. Currently, only Google Cloud Storage is supported. So output_url_prefix should be of the form: `gs://BUCKET_NAME[/NAMESPACE_PATH]`, where `BUCKET_NAME` is the name of the Cloud Storage bucket and `NAMESPACE_PATH` is an optional Cloud Storage namespace path (this is not a Cloud Datastore namespace). For more information about Cloud Storage namespace paths, see [Object name considerations](https://cloud.google.com/storage/docs/naming#object-considerations). The resulting files will be nested deeper than the specified URL prefix. The final output URL will be provided in the google.datastore.admin.v1beta1.ExportEntitiesResponse.output_url field. That value should be used for subsequent ImportEntities operations. By nesting the data files deeper, the same Cloud Storage bucket can be used in multiple ExportEntities operations without conflict.
         */
        outputUrlPrefix?: string | null;
    }
    /**
     * The response for google.datastore.admin.v1beta1.DatastoreAdmin.ExportEntities.
     */
    export interface Schema$GoogleDatastoreAdminV1beta1ExportEntitiesResponse {
        /**
         * Location of the output metadata file. This can be used to begin an import into Cloud Datastore (this project or another project). See google.datastore.admin.v1beta1.ImportEntitiesRequest.input_url. Only present if the operation completed successfully.
         */
        outputUrl?: string | null;
    }
    /**
     * Metadata for ImportEntities operations.
     */
    export interface Schema$GoogleDatastoreAdminV1beta1ImportEntitiesMetadata {
        /**
         * Metadata common to all Datastore Admin operations.
         */
        common?: Schema$GoogleDatastoreAdminV1beta1CommonMetadata;
        /**
         * Description of which entities are being imported.
         */
        entityFilter?: Schema$GoogleDatastoreAdminV1beta1EntityFilter;
        /**
         * The location of the import metadata file. This will be the same value as the google.datastore.admin.v1beta1.ExportEntitiesResponse.output_url field.
         */
        inputUrl?: string | null;
        /**
         * An estimate of the number of bytes processed.
         */
        progressBytes?: Schema$GoogleDatastoreAdminV1beta1Progress;
        /**
         * An estimate of the number of entities processed.
         */
        progressEntities?: Schema$GoogleDatastoreAdminV1beta1Progress;
    }
    /**
     * The request for google.datastore.admin.v1beta1.DatastoreAdmin.ImportEntities.
     */
    export interface Schema$GoogleDatastoreAdminV1beta1ImportEntitiesRequest {
        /**
         * Optionally specify which kinds/namespaces are to be imported. If provided, the list must be a subset of the EntityFilter used in creating the export, otherwise a FAILED_PRECONDITION error will be returned. If no filter is specified then all entities from the export are imported.
         */
        entityFilter?: Schema$GoogleDatastoreAdminV1beta1EntityFilter;
        /**
         * The full resource URL of the external storage location. Currently, only Google Cloud Storage is supported. So input_url should be of the form: `gs://BUCKET_NAME[/NAMESPACE_PATH]/OVERALL_EXPORT_METADATA_FILE`, where `BUCKET_NAME` is the name of the Cloud Storage bucket, `NAMESPACE_PATH` is an optional Cloud Storage namespace path (this is not a Cloud Datastore namespace), and `OVERALL_EXPORT_METADATA_FILE` is the metadata file written by the ExportEntities operation. For more information about Cloud Storage namespace paths, see [Object name considerations](https://cloud.google.com/storage/docs/naming#object-considerations). For more information, see google.datastore.admin.v1beta1.ExportEntitiesResponse.output_url.
         */
        inputUrl?: string | null;
        /**
         * Client-assigned labels.
         */
        labels?: {
            [key: string]: string;
        } | null;
    }
    /**
     * Measures the progress of a particular metric.
     */
    export interface Schema$GoogleDatastoreAdminV1beta1Progress {
        /**
         * The amount of work that has been completed. Note that this may be greater than work_estimated.
         */
        workCompleted?: string | null;
        /**
         * An estimate of how much work needs to be performed. May be zero if the work estimate is unavailable.
         */
        workEstimated?: string | null;
    }
    /**
     * Metadata common to all Datastore Admin operations.
     */
    export interface Schema$GoogleDatastoreAdminV1CommonMetadata {
        /**
         * The time the operation ended, either successfully or otherwise.
         */
        endTime?: string | null;
        /**
         * The client-assigned labels which were provided when the operation was created. May also include additional labels.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The type of the operation. Can be used as a filter in ListOperationsRequest.
         */
        operationType?: string | null;
        /**
         * The time that work began on the operation.
         */
        startTime?: string | null;
        /**
         * The current state of the Operation.
         */
        state?: string | null;
    }
    /**
     * Metadata for Datastore to Firestore migration operations. The DatastoreFirestoreMigration operation is not started by the end-user via an explicit "creation" method. This is an intentional deviation from the LRO design pattern. This singleton resource can be accessed at: "projects/{project_id\}/operations/datastore-firestore-migration"
     */
    export interface Schema$GoogleDatastoreAdminV1DatastoreFirestoreMigrationMetadata {
        /**
         * The current state of migration from Cloud Datastore to Cloud Firestore in Datastore mode.
         */
        migrationState?: string | null;
        /**
         * The current step of migration from Cloud Datastore to Cloud Firestore in Datastore mode.
         */
        migrationStep?: string | null;
    }
    /**
     * Identifies a subset of entities in a project. This is specified as combinations of kinds and namespaces (either or both of which may be all, as described in the following examples). Example usage: Entire project: kinds=[], namespace_ids=[] Kinds Foo and Bar in all namespaces: kinds=['Foo', 'Bar'], namespace_ids=[] Kinds Foo and Bar only in the default namespace: kinds=['Foo', 'Bar'], namespace_ids=[''] Kinds Foo and Bar in both the default and Baz namespaces: kinds=['Foo', 'Bar'], namespace_ids=['', 'Baz'] The entire Baz namespace: kinds=[], namespace_ids=['Baz']
     */
    export interface Schema$GoogleDatastoreAdminV1EntityFilter {
        /**
         * If empty, then this represents all kinds.
         */
        kinds?: string[] | null;
        /**
         * An empty list represents all namespaces. This is the preferred usage for projects that don't use namespaces. An empty string element represents the default namespace. This should be used if the project has data in non-default namespaces, but doesn't want to include them. Each namespace in this list must be unique.
         */
        namespaceIds?: string[] | null;
    }
    /**
     * Metadata for ExportEntities operations.
     */
    export interface Schema$GoogleDatastoreAdminV1ExportEntitiesMetadata {
        /**
         * Metadata common to all Datastore Admin operations.
         */
        common?: Schema$GoogleDatastoreAdminV1CommonMetadata;
        /**
         * Description of which entities are being exported.
         */
        entityFilter?: Schema$GoogleDatastoreAdminV1EntityFilter;
        /**
         * Location for the export metadata and data files. This will be the same value as the google.datastore.admin.v1.ExportEntitiesRequest.output_url_prefix field. The final output location is provided in google.datastore.admin.v1.ExportEntitiesResponse.output_url.
         */
        outputUrlPrefix?: string | null;
        /**
         * An estimate of the number of bytes processed.
         */
        progressBytes?: Schema$GoogleDatastoreAdminV1Progress;
        /**
         * An estimate of the number of entities processed.
         */
        progressEntities?: Schema$GoogleDatastoreAdminV1Progress;
    }
    /**
     * The response for google.datastore.admin.v1.DatastoreAdmin.ExportEntities.
     */
    export interface Schema$GoogleDatastoreAdminV1ExportEntitiesResponse {
        /**
         * Location of the output metadata file. This can be used to begin an import into Cloud Datastore (this project or another project). See google.datastore.admin.v1.ImportEntitiesRequest.input_url. Only present if the operation completed successfully.
         */
        outputUrl?: string | null;
    }
    /**
     * Metadata for ImportEntities operations.
     */
    export interface Schema$GoogleDatastoreAdminV1ImportEntitiesMetadata {
        /**
         * Metadata common to all Datastore Admin operations.
         */
        common?: Schema$GoogleDatastoreAdminV1CommonMetadata;
        /**
         * Description of which entities are being imported.
         */
        entityFilter?: Schema$GoogleDatastoreAdminV1EntityFilter;
        /**
         * The location of the import metadata file. This will be the same value as the google.datastore.admin.v1.ExportEntitiesResponse.output_url field.
         */
        inputUrl?: string | null;
        /**
         * An estimate of the number of bytes processed.
         */
        progressBytes?: Schema$GoogleDatastoreAdminV1Progress;
        /**
         * An estimate of the number of entities processed.
         */
        progressEntities?: Schema$GoogleDatastoreAdminV1Progress;
    }
    /**
     * Metadata for Index operations.
     */
    export interface Schema$GoogleDatastoreAdminV1IndexOperationMetadata {
        /**
         * Metadata common to all Datastore Admin operations.
         */
        common?: Schema$GoogleDatastoreAdminV1CommonMetadata;
        /**
         * The index resource ID that this operation is acting on.
         */
        indexId?: string | null;
        /**
         * An estimate of the number of entities processed.
         */
        progressEntities?: Schema$GoogleDatastoreAdminV1Progress;
    }
    /**
     * An event signifying the start of a new step in a [migration from Cloud Datastore to Cloud Firestore in Datastore mode](https://cloud.google.com/datastore/docs/upgrade-to-firestore).
     */
    export interface Schema$GoogleDatastoreAdminV1MigrationProgressEvent {
        /**
         * Details for the `PREPARE` step.
         */
        prepareStepDetails?: Schema$GoogleDatastoreAdminV1PrepareStepDetails;
        /**
         * Details for the `REDIRECT_WRITES` step.
         */
        redirectWritesStepDetails?: Schema$GoogleDatastoreAdminV1RedirectWritesStepDetails;
        /**
         * The step that is starting. An event with step set to `START` indicates that the migration has been reverted back to the initial pre-migration state.
         */
        step?: string | null;
    }
    /**
     * An event signifying a change in state of a [migration from Cloud Datastore to Cloud Firestore in Datastore mode](https://cloud.google.com/datastore/docs/upgrade-to-firestore).
     */
    export interface Schema$GoogleDatastoreAdminV1MigrationStateEvent {
        /**
         * The new state of the migration.
         */
        state?: string | null;
    }
    /**
     * Details for the `PREPARE` step.
     */
    export interface Schema$GoogleDatastoreAdminV1PrepareStepDetails {
        /**
         * The concurrency mode this database will use when it reaches the `REDIRECT_WRITES` step.
         */
        concurrencyMode?: string | null;
    }
    /**
     * Measures the progress of a particular metric.
     */
    export interface Schema$GoogleDatastoreAdminV1Progress {
        /**
         * The amount of work that has been completed. Note that this may be greater than work_estimated.
         */
        workCompleted?: string | null;
        /**
         * An estimate of how much work needs to be performed. May be zero if the work estimate is unavailable.
         */
        workEstimated?: string | null;
    }
    /**
     * Details for the `REDIRECT_WRITES` step.
     */
    export interface Schema$GoogleDatastoreAdminV1RedirectWritesStepDetails {
        /**
         * Ths concurrency mode for this database.
         */
        concurrencyMode?: string | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$GoogleLongrunningOperation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal response of the operation in case of success. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Exports a copy of all or a subset of entities from Google Cloud Datastore to another storage system, such as Google Cloud Storage. Recent updates to entities may not be reflected in the export. The export occurs in the background and its progress can be monitored and managed via the Operation resource that is created. The output of an export may only be used once the associated operation is done. If an export operation is cancelled before completion it may leave partial data behind in Google Cloud Storage.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/datastore.googleapis.com
         * // - Login into gcloud by running:
         * //   `$ gcloud auth application-default login`
         * // - Install the npm module by running:
         * //   `$ npm install googleapis`
         *
         * const {google} = require('googleapis');
         * const datastore = google.datastore('v1beta1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/datastore',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await datastore.projects.export({
         *     // Project ID against which to make the request.
         *     projectId: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "entityFilter": {},
         *       //   "labels": {},
         *       //   "outputUrlPrefix": "my_outputUrlPrefix"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        export(params: Params$Resource$Projects$Export, options: StreamMethodOptions): GaxiosPromise<Readable>;
        export(params?: Params$Resource$Projects$Export, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        export(params: Params$Resource$Projects$Export, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        export(params: Params$Resource$Projects$Export, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        export(params: Params$Resource$Projects$Export, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        export(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Imports entities into Google Cloud Datastore. Existing entities with the same key are overwritten. The import occurs in the background and its progress can be monitored and managed via the Operation resource that is created. If an ImportEntities operation is cancelled, it is possible that a subset of the data has already been imported to Cloud Datastore.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/datastore.googleapis.com
         * // - Login into gcloud by running:
         * //   `$ gcloud auth application-default login`
         * // - Install the npm module by running:
         * //   `$ npm install googleapis`
         *
         * const {google} = require('googleapis');
         * const datastore = google.datastore('v1beta1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/datastore',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await datastore.projects.import({
         *     // Project ID against which to make the request.
         *     projectId: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "entityFilter": {},
         *       //   "inputUrl": "my_inputUrl",
         *       //   "labels": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        import(params: Params$Resource$Projects$Import, options: StreamMethodOptions): GaxiosPromise<Readable>;
        import(params?: Params$Resource$Projects$Import, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        import(params: Params$Resource$Projects$Import, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        import(params: Params$Resource$Projects$Import, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        import(params: Params$Resource$Projects$Import, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        import(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
    }
    export interface Params$Resource$Projects$Export extends StandardParameters {
        /**
         * Project ID against which to make the request.
         */
        projectId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleDatastoreAdminV1beta1ExportEntitiesRequest;
    }
    export interface Params$Resource$Projects$Import extends StandardParameters {
        /**
         * Project ID against which to make the request.
         */
        projectId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleDatastoreAdminV1beta1ImportEntitiesRequest;
    }
    export {};
}
