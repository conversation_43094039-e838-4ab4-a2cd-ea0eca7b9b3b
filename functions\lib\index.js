"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.validatePurchase = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const googleapis_1 = require("googleapis");
// Correctly import the JSON file using the 'import' statement
const serviceAccount = __importStar(require("./service-account.json"));
// Initialize Firebase Admin SDK
admin.initializeApp();
const firestore = admin.firestore();
// IMPORTANT: Replace with your app's actual package name
const packageName = "com.your.app.package_name";
/**
 * A callable Cloud Function to securely validate a Google Play purchase
 * and grant credits to the user.
 */
// The fix is here: Explicitly type 'data' and 'context'
exports.validatePurchase = functions.https.onCall(async (data, context) => {
    // 1. Check for authentication. The 'auth' property is now correctly typed.
    if (!context.auth) {
        throw new functions.https.HttpsError("unauthenticated", "You must be logged in to make a purchase.");
    }
    // The 'data' object is now correctly understood to be the client payload.
    const { purchaseToken, productId } = data;
    const uid = context.auth.uid;
    // 2. Initialize the Google Play API client
    const authClient = new googleapis_1.google.auth.GoogleAuth({
        credentials: {
            client_email: serviceAccount.client_email,
            private_key: serviceAccount.private_key,
        },
        scopes: ["https://www.googleapis.com/auth/androidpublisher"],
    });
    const androidPublisher = googleapis_1.google.androidpublisher({
        version: "v3",
        auth: authClient,
    });
    try {
        // 3. Verify the purchase with Google Play Developer API
        const res = await androidpublisher.purchases.products.get({
            packageName: packageName,
            productId: productId,
            token: purchaseToken,
        });
        // 4. Perform crucial checks on the response
        if (res.data.purchaseState !== 0) { // 0 is PURCHASED
            throw new functions.https.HttpsError("failed-precondition", "Purchase is not valid or is pending.");
        }
        if (res.data.consumptionState !== 0) { // 0 is NOT_CONSUMED
            throw new functions.https.HttpsError("failed-precondition", "Purchase has already been consumed.");
        }
        // 5. Grant credits in a Firestore transaction
        const userDocRef = firestore.collection("users").doc(uid);
        let creditsToAdd = 0;
        if (productId === "calculation_credits_3") {
            creditsToAdd = 3;
        }
        // Add more else-if blocks here for other products
        if (creditsToAdd === 0) {
            throw new functions.https.HttpsError("invalid-argument", "Unknown product ID.");
        }
        await firestore.runTransaction(async (transaction) => {
            var _a;
            const userDoc = await transaction.get(userDocRef);
            if (!userDoc.exists) {
                throw new functions.https.HttpsError("not-found", "User document not found.");
            }
            const currentCredits = ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.calculationCredits) || 0;
            const newCredits = currentCredits + creditsToAdd;
            transaction.update(userDocRef, { calculationCredits: newCredits });
        });
        // 6. Acknowledge the purchase with Google Play
        await androidpublisher.purchases.products.acknowledge({
            packageName: packageName,
            productId: productId,
            token: purchaseToken,
        });
        return { status: "success", newCredits: "updated" };
    }
    catch (error) {
        functions.logger.error("Error validating purchase:", error);
        throw new functions.https.HttpsError("internal", "An error occurred while validating the purchase.", error);
    }
});
//# sourceMappingURL=index.js.map