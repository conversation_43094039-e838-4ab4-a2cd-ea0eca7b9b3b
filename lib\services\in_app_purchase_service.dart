import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

/// Service class to handle all in-app purchase operations
/// This service manages the connection to the app store, product loading,
/// purchase initiation, and purchase verification
class InAppPurchaseService {
  InAppPurchaseService._();
  static final InAppPurchaseService instance = InAppPurchaseService._();

  // In-app purchase instance
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  
  // Stream subscription for purchase updates
  StreamSubscription<List<PurchaseDetails>>? _subscription;
  
  // Available products from the store
  List<ProductDetails> _products = [];
  
  // Purchase state management
  bool _isAvailable = false;
  bool _purchasePending = false;
  String? _queryProductError;
  
  // Product IDs - add more product IDs here as needed
  static const Set<String> _productIds = {'calculation_credits_3'};
  
  // Getters for UI to access state
  List<ProductDetails> get products => _products;
  bool get isAvailable => _isAvailable;
  bool get purchasePending => _purchasePending;
  String? get queryProductError => _queryProductError;

  /// Initialize the in-app purchase service
  /// This should be called once when the app starts or when entering the store screen
  Future<void> initialize() async {
    try {
      debugPrint('Initializing InAppPurchaseService...');
      
      // Check if the store is available on this device
      _isAvailable = await _inAppPurchase.isAvailable();
      
      if (!_isAvailable) {
        debugPrint('In-app purchases not available on this device');
        return;
      }
      
      debugPrint('In-app purchases available, setting up purchase stream...');
      
      // Set up the purchase stream listener
      _setupPurchaseStreamListener();
      
      // Load available products
      await _loadProducts();
      
      debugPrint('InAppPurchaseService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing InAppPurchaseService: $e');
      rethrow;
    }
  }

  /// Set up the purchase stream listener to handle purchase updates
  void _setupPurchaseStreamListener() {
    _subscription = _inAppPurchase.purchaseStream.listen(
      (List<PurchaseDetails> purchaseDetailsList) {
        _handlePurchaseUpdate(purchaseDetailsList);
      },
      onDone: () {
        debugPrint('Purchase stream closed');
        _subscription?.cancel();
      },
      onError: (error) {
        debugPrint('Purchase stream error: $error');
      },
    );
  }

  /// Handle purchase updates from the store
  void _handlePurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchase in purchaseDetailsList) {
      debugPrint('Processing purchase: ${purchase.productID}, Status: ${purchase.status}');
      
      switch (purchase.status) {
        case PurchaseStatus.pending:
          _handlePendingPurchase(purchase);
          break;
          
        case PurchaseStatus.purchased:
          _handleSuccessfulPurchase(purchase);
          break;
          
        case PurchaseStatus.error:
          _handleFailedPurchase(purchase);
          break;
          
        case PurchaseStatus.restored:
          // For consumable products, we typically don't restore
          // But we still need to complete the purchase
          debugPrint('Purchase restored: ${purchase.productID}');
          _inAppPurchase.completePurchase(purchase);
          break;
          
        case PurchaseStatus.canceled:
          debugPrint('Purchase canceled: ${purchase.productID}');
          _purchasePending = false;
          break;
      }
    }
  }

  /// Handle pending purchase state
  void _handlePendingPurchase(PurchaseDetails purchase) {
    debugPrint('Purchase pending for product: ${purchase.productID}');
    _purchasePending = true;
    
    // Show loading indicator to user
    // In a real app, you might want to use a state management solution
    // to notify the UI about this state change
    print('PURCHASE_PENDING: Showing loading indicator to user...');
  }

  /// Handle successful purchase
  void _handleSuccessfulPurchase(PurchaseDetails purchase) {
    debugPrint('Purchase successful for product: ${purchase.productID}');
    _purchasePending = false;
    
    // Show success message but don't grant credits yet
    print('PURCHASE_SUCCESS: Purchase successful, now verifying with our server...');
    
    // Verify the purchase on the server before granting credits
    _verifyPurchaseOnServer(purchase);
  }

  /// Handle failed purchase
  void _handleFailedPurchase(PurchaseDetails purchase) {
    debugPrint('Purchase failed for product: ${purchase.productID}');
    debugPrint('Error: ${purchase.error}');
    _purchasePending = false;

    // Show error message to user
    print('PURCHASE_ERROR: ${purchase.error?.message ?? "Purchase failed"}');

    // IMPORTANT: Complete the purchase to clear it from the queue
    _inAppPurchase.completePurchase(purchase);
  }

  /// Verify purchase on server before granting credits
  /// This is a placeholder function where you'll call your Firebase Cloud Function
  Future<void> _verifyPurchaseOnServer(PurchaseDetails purchaseDetails) async {
    try {
      // Get a callable function reference
      final HttpsCallable callable = FirebaseFunctions.instance.httpsCallable('validatePurchase');

      // Call the function with the required parameters
      final result = await callable.call<Map<String, dynamic>>({
        'purchaseToken': purchaseDetails.verificationData.serverVerificationData,
        'productId': purchaseDetails.productID,
      });

      // Check if the function executed successfully
      if (result.data['status'] == 'success') {
        print("Server validation successful. Completing purchase.");
        // IMPORTANT: Only complete the purchase after successful server validation
        await InAppPurchase.instance.completePurchase(purchaseDetails);
      } else {
        // Handle cases where the server returns a non-success status
        print("Server validation failed: ${result.data['message']}");
        // Optionally, show an error to the user.
        // Do NOT complete the purchase here.
      }
    } on FirebaseFunctionsException catch (e) {
      // Handle specific Firebase Cloud Function errors
      print("Cloud Function Error: ${e.code} - ${e.message}");
      // Inform the user that verification failed and they should contact support if charged.
      // Do NOT complete the purchase here.
    } catch (e) {
      // Handle other generic errors
      print("An unknown error occurred during server verification: $e");
      // Do NOT complete the purchase here.
    }
  }
  
  /// Load available products from the store
  Future<void> _loadProducts() async {
    try {
      debugPrint('Loading products from store...');

      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(_productIds);

      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('Products not found: ${response.notFoundIDs}');
        _queryProductError = 'Some products not found: ${response.notFoundIDs.join(', ')}';
      }

      if (response.error != null) {
        debugPrint('Error loading products: ${response.error}');
        _queryProductError = response.error!.message;
        return;
      }

      _products = response.productDetails;
      _queryProductError = null;

      debugPrint('Loaded ${_products.length} products:');
      for (final product in _products) {
        debugPrint('- ${product.id}: ${product.title} - ${product.price}');
      }

    } catch (e) {
      debugPrint('Error loading products: $e');
      _queryProductError = 'Failed to load products: $e';
      rethrow;
    }
  }

  /// Initiate a purchase for the given product
  /// This method will be called by the UI's "Buy" button
  Future<void> buyProduct(ProductDetails productDetails) async {
    try {
      debugPrint('Initiating purchase for product: ${productDetails.id}');

      if (!_isAvailable) {
        throw Exception('In-app purchases not available');
      }

      if (_purchasePending) {
        throw Exception('Another purchase is already in progress');
      }

      // Check if user is authenticated
      final User? currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User must be logged in to make purchases');
      }

      // Create purchase param for consumable product
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );

      // Initiate the purchase
      final bool success = await _inAppPurchase.buyConsumable(
        purchaseParam: purchaseParam,
      );

      if (!success) {
        throw Exception('Failed to initiate purchase');
      }

      debugPrint('Purchase initiated successfully');

    } catch (e) {
      debugPrint('Error initiating purchase: $e');
      rethrow;
    }
  }

  /// Dispose of the service and clean up resources
  void dispose() {
    debugPrint('Disposing InAppPurchaseService...');
    _subscription?.cancel();
    _subscription = null;
  }

  /// Restore purchases (mainly for iOS, but good to have)
  Future<void> restorePurchases() async {
    try {
      debugPrint('Restoring purchases...');
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      debugPrint('Error restoring purchases: $e');
      rethrow;
    }
  }

  /// Get a specific product by ID
  ProductDetails? getProductById(String productId) {
    try {
      return _products.firstWhere((product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }
}
